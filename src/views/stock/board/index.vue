<template>
  <div class="app-container">
    <el-card shadow="never" class="box-card form-card mb10">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
        <el-row class=" form_row">
          <el-row class="form_col">
            <el-form-item prop="warehouseName">
              <el-input v-model="queryParams.warehouseName" placeholder="仓库名称" clearable />
            </el-form-item>
          </el-row>
        </el-row>
        <el-row>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <template v-if="toggleSearchDom">
              <el-button type="text" @click="packUp">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
              </el-button>
            </template>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

  </div>
</template>

<script>
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  mixins: [tableUi],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warehouseName: '',
      },

    };
  },
  components: {

  },
  created() {

  },
  methods: {}
};
</script>

<style lang="scss" scoped></style>
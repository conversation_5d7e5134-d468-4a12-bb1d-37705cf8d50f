<template>
  <div class="app-container">
    <!-- 库存筛选 -->
    <el-card shadow="never" class="box-card form-card mb10">
      <div class="filter-header">
        <span class="filter-title">库存筛选</span>
      </div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
        <el-row class="form_row">
          <el-row class="form_col">
            <el-form-item label="仓库选择" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="全部" clearable style="width: 200px;">
                <el-option
                  v-for="item in warehouseList"
                  :key="item.warehouseId"
                  :label="item.warehouseName"
                  :value="item.warehouseId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="入库时间" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 300px;">
              </el-date-picker>
            </el-form-item>
          </el-row>
        </el-row>
        <el-row>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <template v-if="toggleSearchDom">
              <el-button type="text" @click="packUp">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
              </el-button>
            </template>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <!-- 仓库分布图 -->
    <el-card shadow="never" class="mb10">
      <WarehouseDistribution
        :warehouses="warehouseDistribution"
        @onViewMore="handleViewMore"
      />
    </el-card>

    <!-- 库存总量和批次分布 -->
    <el-card shadow="never" class="mb10">
      <InventorySummary
        :summaryData="inventorySummaryData"
        :batchData="batchDistributionData"
      />
    </el-card>

    <!-- 库存明细表格 -->
    <el-card shadow="never" class="table_box card_radius_b">
      <InventoryTable
        :tableData="inventoryTableData"
        :loading="tableLoading"
        :pagination="tablePagination"
        :warehouseList="warehouseList"
        @onPageChange="handlePageChange"
        @onPageSizeChange="handlePageSizeChange"
        @onDetail="handleDetail"
      />
    </el-card>

  </div>
</template>

<script>
import { tableUi } from "@/utils/mixin/tableUi.js";
import { warehouseList } from "@/api/basics/index.js";
import WarehouseDistribution from './components/WarehouseDistribution.vue';
import InventorySummary from './components/InventorySummary.vue';
import InventoryTable from './components/InventoryTable.vue';

export default {
  mixins: [tableUi],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warehouseId: '',
        startTime: '',
        endTime: ''
      },
      warehouseList: [], // 仓库列表
      dateRange: [], // 日期范围

      // 仓库分布数据
      warehouseDistribution: [],

      // 库存总量数据
      inventorySummaryData: {},

      // 批次分布数据
      batchDistributionData: {},

      // 表格数据
      inventoryTableData: [],
      tableLoading: false,
      tablePagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  components: {
    WarehouseDistribution,
    InventorySummary,
    InventoryTable
  },
  created() {
    this.getWarehouseList();
    this.getAllData();
  },
  methods: {
    // 获取仓库列表
    async getWarehouseList() {
      try {
        const res = await warehouseList();
        if (res.code === 200) {
          this.warehouseList = res.result || [];
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
      }
    },

    // 获取所有数据
    async getAllData() {
      await Promise.all([
        this.getWarehouseDistribution(),
        this.getInventorySummary(),
        this.getBatchDistribution(),
        this.getInventoryTableData()
      ]);
    },

    // 获取仓库分布数据
    async getWarehouseDistribution() {
      // 模拟数据，实际应该调用API
      this.warehouseDistribution = [
        { warehouseId: 1, warehouseName: 'A-01', stockLevel: 85 },
        { warehouseId: 2, warehouseName: 'A-02', stockLevel: 65 },
        { warehouseId: 3, warehouseName: 'A-03', stockLevel: 45 },
        { warehouseId: 4, warehouseName: 'A-04', stockLevel: 75 },
        { warehouseId: 5, warehouseName: 'B-01', stockLevel: 35 },
        { warehouseId: 6, warehouseName: 'B-02', stockLevel: 55 },
        { warehouseId: 7, warehouseName: 'B-03', stockLevel: 90 },
        { warehouseId: 8, warehouseName: 'B-04', stockLevel: 70 },
        { warehouseId: 9, warehouseName: 'C-01', stockLevel: 40 },
        { warehouseId: 10, warehouseName: 'C-02', stockLevel: 60 }
      ];
    },

    // 获取库存总量数据
    async getInventorySummary() {
      this.inventorySummaryData = {
        totalCount: 192,
        totalWeight: 12458,
        lowStockCount: 20,
        stagnantCount: 18
      };
    },

    // 获取批次分布数据
    async getBatchDistribution() {
      this.batchDistributionData = {
        oneMonth: 45,
        threeMonth: 25,
        sixMonth: 20,
        overSixMonth: 10
      };
    },

    // 获取库存表格数据
    async getInventoryTableData() {
      this.tableLoading = true;
      try {
        // 模拟数据，实际应该调用API
        this.inventoryTableData = [
          {
            inventoryCode: 'RK20241107071',
            inventoryType: '急冻入库',
            warehouseName: '急冻库',
            inventoryCount: 136,
            inventoryWeight: 1776,
            operator: '库管员',
            inventoryTime: '2024-11-07 07:16:09',
            stockLevel: '45天',
            status: '1'
          },
          {
            inventoryCode: 'RK20241107072',
            inventoryType: '急冻入库',
            warehouseName: '急冻库',
            inventoryCount: 136,
            inventoryWeight: 1776,
            operator: '库管员',
            inventoryTime: '2024-11-07 07:16:09',
            stockLevel: '60天',
            status: '2'
          },
          {
            inventoryCode: 'RK20241107073',
            inventoryType: '急冻入库',
            warehouseName: '急冻库',
            inventoryCount: 136,
            inventoryWeight: 1776,
            operator: '库管员',
            inventoryTime: '2024-11-07 07:16:09',
            stockLevel: '45天',
            status: '1'
          },
          {
            inventoryCode: 'RK20241107074',
            inventoryType: '急冻入库',
            warehouseName: '急冻库',
            inventoryCount: 136,
            inventoryWeight: 1776,
            operator: '库管员',
            inventoryTime: '2024-11-07 07:16:09',
            stockLevel: '90天',
            status: '2'
          }
        ];
        this.tablePagination.total = 102;
      } catch (error) {
        console.error('获取表格数据失败:', error);
      } finally {
        this.tableLoading = false;
      }
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handleDateRange();
      this.getAllData();
    },

    // 重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        warehouseId: '',
        startTime: '',
        endTime: ''
      };
      this.dateRange = [];
      this.handleQuery();
    },

    // 处理日期范围
    handleDateRange() {
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = '';
        this.queryParams.endTime = '';
      }
    },

    // 仓库分布查看更多
    handleViewMore(showAll) {
      console.log('查看更多仓库:', showAll);
    },

    // 表格分页
    handlePageChange(page) {
      this.tablePagination.pageNum = page;
      this.getInventoryTableData();
    },

    handlePageSizeChange(size) {
      this.tablePagination.pageSize = size;
      this.tablePagination.pageNum = 1;
      this.getInventoryTableData();
    },

    // 查看详情
    handleDetail(row) {
      console.log('查看详情:', row);
      // TODO: 实现详情查看逻辑
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  background: #F7F8FA;
  min-height: calc(100vh - 84px);
}

.filter-header {
  margin-bottom: 20px;

  .filter-title {
    font-size: 16px;
    font-weight: 500;
    color: #1D2129;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #165DFF;
      border-radius: 2px;
    }
  }
}

.el-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);

  &.mb10 {
    margin-bottom: 16px;
  }

  ::v-deep .el-card__body {
    padding: 24px;
  }
}

.form_box {
  .el-form-item {
    margin-bottom: 16px;

    .el-form-item__label {
      font-weight: 500;
      color: #1D2129;
    }
  }

  .el-select,
  .el-input,
  .el-date-editor {
    .el-input__inner {
      border: 1px solid #E5E6EB;
      border-radius: 6px;

      &:focus {
        border-color: #165DFF;
        box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
      }
    }
  }

  .el-button {
    border-radius: 6px;
    font-weight: 500;

    &.el-button--primary {
      background: #165DFF;
      border-color: #165DFF;

      &:hover {
        background: #4080FF;
        border-color: #4080FF;
      }
    }

    &.el-button--default {
      color: #4E5969;
      border-color: #E5E6EB;

      &:hover {
        color: #165DFF;
        border-color: #165DFF;
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .warehouse-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .warehouse-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .el-col {
    margin-bottom: 16px;
  }
}
</style>
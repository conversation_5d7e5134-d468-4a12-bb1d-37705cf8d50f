好的 👍，我帮你把 **一、页面结构与模块划分、二、组件划分与关系、三、交互逻辑与数据流** 三部分整理成 Markdown 文档：

---

# 仓库库存管理页面设计文档

## 一、页面结构与模块划分

页面整体由以下几个模块构成：

1. **库存筛选模块（FilterBar）**

   * 功能：提供仓库选择、入库时间区间、查询/重置操作。
   * 输出：筛选条件，触发数据刷新。

2. **仓库分布图模块（WarehouseDistribution）**

   * 功能：展示仓库库存分布情况。
   * 默认显示前 8 个仓库，超过 8 个显示“查看更多”按钮。
   * 卡片颜色根据库存状态（高/中/低库存）变化。

3. **库存总量 & 批次分布模块（InventorySummary）**

   * 功能：综合展示库存总量与批次分布。
   * 内容包括：

     * 库存总量（环形图：总入库数、总重量、低库存预警、呆滞库存）。
     * 批次分布（饼图：1个月、1-3个月、3-6个月、6个月以上）。

4. **库存总量表格模块（InventoryTable）**

   * 功能：展示库存明细数据。
   * 支持分页、查询、状态展示、详情查看。
   * 表格风格与项目其他模块保持一致。

---

## 二、组件划分与关系

```text
src/views/stock/board/index.vue（仓库库存页面）
│
├─ FilterBar（筛选条件组件）
│    ├─ emits: onSearch(params), onReset()
│    └─ props: 默认筛选条件
│
├─ WarehouseDistribution（仓库分布图组件）
│    ├─ props: warehouses（仓库数据列表）
│    ├─ emits: onViewMore()
│    └─ state: 展示前8个 or 全部
│
├─ InventorySummary（库存总量&批次分布组件）
│    ├─ props: summaryData（环形图数据）、batchData（批次分布数据）
│
└─ InventoryTable（库存明细表格组件）
     ├─ props: tableData（表格数据）
     ├─ emits: onPageChange(page), onPageSizeChange(size)
```

---

## 三、交互逻辑与数据流

### 1. 筛选逻辑

* 用户在 **FilterBar** 输入条件后点击「查询」：

  * 触发 API 请求，获取以下数据：

    * 仓库分布数据（仓库库存量及状态）。
    * 库存总量与批次分布数据。
    * 库存明细表格数据。
  * 结果分发到子组件 **WarehouseDistribution**、**InventorySummary**、**InventoryTable**。

* 点击「重置」：

  * 清空筛选条件，触发一次默认查询，刷新所有子组件数据。

---

### 2. 仓库分布图（WarehouseDistribution）

* 接收仓库数据 `warehouses`。
* 默认展示前 8 个仓库。
* 超过 8 个仓库时，显示「查看更多」按钮。
* 点击「查看更多」：触发 `onViewMore` 事件（仅事件触发，不做后续逻辑）。

---

### 3. 库存总量与批次分布（InventorySummary）

* 接收 `summaryData` 和 `batchData`。
* 根据数据渲染环形图与饼图。
* 作为一个整体组件，外层只需传数据，内部自渲染。

---

### 4. 库存总量表格（InventoryTable）

* 接收 `tableData`，展示库存明细。
* 支持分页，触发 `onPageChange`、`onPageSizeChange` 事件重新请求数据。
* 操作列包括「详情」按钮，触发跳转或弹窗。

<template>
  <div class="inventory-summary">
    <el-row :gutter="20">
      <!-- 库存总量 -->
      <el-col :span="12">
        <div class="summary-card">
          <div class="card-title">库存总量</div>
          <div class="chart-container">
            <div id="inventoryChart" class="chart"></div>
            <div class="chart-data">
              <div class="data-item">
                <span class="data-label">入库总数</span>
                <span class="data-value">{{ summaryData.totalCount || 192 }} 条</span>
              </div>
              <div class="data-item">
                <span class="data-label">入库总重量</span>
                <span class="data-value">{{ summaryData.totalWeight || 12458 }} kg</span>
              </div>
              <div class="data-item">
                <span class="data-label">低库存预警</span>
                <span class="data-value warning">{{ summaryData.lowStockCount || 20 }} 条</span>
              </div>
              <div class="data-item alert">
                <span class="alert-icon">!</span>
                <span class="data-label">呆滞库存</span>
                <span class="data-value">{{ summaryData.stagnantCount || 18 }} 条</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      
      <!-- 批次分布 -->
      <el-col :span="12">
        <div class="summary-card">
          <div class="card-title">批次分布</div>
          <div class="chart-container">
            <div id="batchChart" class="chart"></div>
            <div class="batch-legend">
              <div class="legend-item">
                <span class="legend-color" style="background: #165DFF;"></span>
                <span class="legend-label">近1个月入库</span>
                <span class="legend-value">{{ batchData.oneMonth || 45 }} %</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #00B42A;"></span>
                <span class="legend-label">近1-3个月入库</span>
                <span class="legend-value">{{ batchData.threeMonth || 25 }} %</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #FF7D00;"></span>
                <span class="legend-label">近3-6个月入库</span>
                <span class="legend-value">{{ batchData.sixMonth || 20 }} %</span>
              </div>
              <div class="legend-item">
                <span class="legend-color" style="background: #F53F3F;"></span>
                <span class="legend-label">近6个月以上</span>
                <span class="legend-value">{{ batchData.overSixMonth || 10 }} %</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'InventorySummary',
  props: {
    summaryData: {
      type: Object,
      default: () => ({})
    },
    batchData: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initInventoryChart();
      this.initBatchChart();
    });
  },
  watch: {
    summaryData: {
      handler() {
        this.$nextTick(() => {
          this.initInventoryChart();
        });
      },
      deep: true
    },
    batchData: {
      handler() {
        this.$nextTick(() => {
          this.initBatchChart();
        });
      },
      deep: true
    }
  },
  methods: {
    initInventoryChart() {
      const chartDom = document.getElementById('inventoryChart');
      if (!chartDom) return;
      
      const myChart = this.$echarts.init(chartDom);
      const option = {
        series: [{
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['50%', '50%'],
          data: [
            { value: this.summaryData.totalCount || 192, name: '库存总量' }
          ],
          label: {
            show: false
          },
          itemStyle: {
            color: '#165DFF'
          },
          emphasis: {
            disabled: true
          }
        }]
      };
      myChart.setOption(option);
    },
    
    initBatchChart() {
      const chartDom = document.getElementById('batchChart');
      if (!chartDom) return;
      
      const myChart = this.$echarts.init(chartDom);
      const option = {
        series: [{
          type: 'pie',
          radius: '70%',
          center: ['50%', '50%'],
          data: [
            { value: this.batchData.oneMonth || 45, name: '近1个月入库', itemStyle: { color: '#165DFF' } },
            { value: this.batchData.threeMonth || 25, name: '近1-3个月入库', itemStyle: { color: '#00B42A' } },
            { value: this.batchData.sixMonth || 20, name: '近3-6个月入库', itemStyle: { color: '#FF7D00' } },
            { value: this.batchData.overSixMonth || 10, name: '近6个月以上', itemStyle: { color: '#F53F3F' } }
          ],
          label: {
            show: false
          },
          emphasis: {
            disabled: true
          }
        }]
      };
      myChart.setOption(option);
    }
  }
};
</script>

<style lang="scss" scoped>
.inventory-summary {
  .summary-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    height: 300px;
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #165DFF;
      margin-bottom: 20px;
    }
    
    .chart-container {
      display: flex;
      height: 240px;
      
      .chart {
        flex: 1;
        height: 100%;
      }
      
      .chart-data {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        
        .data-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          &.alert {
            background: #FFF7E6;
            border: 1px solid #FFD666;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 0;
            
            .alert-icon {
              background: #FF7D00;
              color: white;
              width: 16px;
              height: 16px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: bold;
              margin-right: 8px;
            }
          }
          
          .data-label {
            font-size: 14px;
            color: #4E5969;
          }
          
          .data-value {
            font-size: 16px;
            font-weight: 500;
            color: #165DFF;
            
            &.warning {
              color: #FF7D00;
            }
          }
        }
      }
      
      .batch-legend {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 20px;
        
        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          
          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 12px;
          }
          
          .legend-label {
            flex: 1;
            font-size: 14px;
            color: #4E5969;
          }
          
          .legend-value {
            font-size: 16px;
            font-weight: 500;
            color: #1D2129;
          }
        }
      }
    }
  }
}
</style>

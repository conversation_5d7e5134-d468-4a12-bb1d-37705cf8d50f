# 仓库库存管理页面

## 页面概述

仓库库存管理页面是一个综合性的库存管理界面，包含库存筛选、仓库分布图、库存总量统计、批次分布分析和库存明细表格等功能模块。

## 功能模块

### 1. 库存筛选模块
- **功能**: 提供仓库选择和入库时间区间筛选
- **组件**: 内置在主页面中
- **交互**: 支持查询和重置操作

### 2. 仓库分布图模块 (WarehouseDistribution)
- **功能**: 展示仓库库存分布情况
- **特性**: 
  - 默认显示前8个仓库
  - 超过8个仓库显示"查看全部"按钮
  - 根据库存状态显示不同颜色（高/中/低库存）
- **颜色规则**:
  - 红色: 高库存 (>80%)
  - 橙色: 中等库存 (50-80%)
  - 绿色: 低库存 (<50%)

### 3. 库存总量和批次分布模块 (InventorySummary)
- **功能**: 综合展示库存总量与批次分布
- **包含内容**:
  - 库存总量（环形图）: 总入库数、总重量、低库存预警、呆滞库存
  - 批次分布（饼图）: 1个月、1-3个月、3-6个月、6个月以上

### 4. 库存明细表格模块 (InventoryTable)
- **功能**: 展示库存明细数据
- **特性**: 
  - 支持分页
  - 支持查询筛选
  - 状态展示
  - 详情查看

## API接口说明

### 需要实现的API接口

```javascript
// 从 @/api/stock.js 导入以下接口

// 1. 获取仓库分布数据
export function getWarehouseDistribution(data) {
    return request({
        url: '/mesapp-service/warehouse/distribution',
        method: 'post',
        data: data
    })
}

// 2. 获取库存总量统计
export function getInventorySummary(data) {
    return request({
        url: '/mesapp-service/warehouse/summary',
        method: 'post',
        data: data
    })
}

// 3. 获取批次分布数据
export function getBatchDistribution(data) {
    return request({
        url: '/mesapp-service/warehouse/batchDistribution',
        method: 'post',
        data: data
    })
}

// 4. 获取库存明细表格数据
export function getInventoryTableData(data) {
    return request({
        url: '/mesapp-service/warehouse/inventoryTable',
        method: 'post',
        data: data
    })
}
```

### 数据格式说明

#### 仓库分布数据格式
```javascript
[
  {
    warehouseId: 1,
    warehouseName: 'A-01',
    stockLevel: 85 // 库存水平百分比
  }
]
```

#### 库存总量数据格式
```javascript
{
  totalCount: 192,      // 入库总数
  totalWeight: 12458,   // 入库总重量
  lowStockCount: 20,    // 低库存预警数量
  stagnantCount: 18     // 呆滞库存数量
}
```

#### 批次分布数据格式
```javascript
{
  oneMonth: 45,        // 近1个月入库百分比
  threeMonth: 25,      // 近1-3个月入库百分比
  sixMonth: 20,        // 近3-6个月入库百分比
  overSixMonth: 10     // 近6个月以上百分比
}
```

#### 库存明细表格数据格式
```javascript
[
  {
    inventoryCode: 'RK20241107071',      // 入库单号
    inventoryType: '急冻入库',            // 入库类型
    warehouseName: '急冻库',             // 仓库名称
    inventoryCount: 136,                 // 入库数量
    inventoryWeight: 1776,               // 入库重量
    operator: '库管员',                  // 操作人
    inventoryTime: '2024-11-07 07:16:09', // 入库时间
    stockLevel: '45天',                  // 库存水平
    status: '1'                          // 状态 1:正常 2:预警
  }
]
```

## 使用方法

### 1. 在主页面中集成所有组件
```vue
<template>
  <div class="app-container">
    <!-- 库存筛选 -->
    <el-card shadow="never" class="box-card form-card mb10">
      <!-- 筛选表单 -->
    </el-card>

    <!-- 仓库分布图 -->
    <el-card shadow="never" class="mb10">
      <WarehouseDistribution 
        :warehouses="warehouseDistribution" 
        @onViewMore="handleViewMore"
      />
    </el-card>

    <!-- 库存总量和批次分布 -->
    <el-card shadow="never" class="mb10">
      <InventorySummary 
        :summaryData="inventorySummaryData" 
        :batchData="batchDistributionData"
      />
    </el-card>

    <!-- 库存明细表格 -->
    <el-card shadow="never" class="table_box card_radius_b">
      <InventoryTable 
        :tableData="inventoryTableData"
        :loading="tableLoading"
        :pagination="tablePagination"
        :warehouseList="warehouseList"
        @onPageChange="handlePageChange"
        @onPageSizeChange="handlePageSizeChange"
        @onDetail="handleDetail"
      />
    </el-card>
  </div>
</template>
```

### 2. 导入组件和API
```javascript
import WarehouseDistribution from './components/WarehouseDistribution.vue';
import InventorySummary from './components/InventorySummary.vue';
import InventoryTable from './components/InventoryTable.vue';
import { warehouseList } from "@/api/basics/index.js";
```

### 3. 实现数据获取方法
参考 `index.vue` 中的实现，将模拟数据替换为实际的API调用。

## 注意事项

1. **样式一致性**: 所有组件都遵循项目的设计规范，使用统一的颜色和字体
2. **响应式设计**: 支持不同屏幕尺寸的适配
3. **数据更新**: 筛选条件变化时会自动刷新所有模块的数据
4. **错误处理**: 建议在实际API调用中添加适当的错误处理逻辑
5. **性能优化**: 大数据量时建议实现虚拟滚动或分页加载

## 开发状态

- [x] 库存筛选模块
- [x] 仓库分布图组件
- [x] 库存总量和批次分布组件  
- [x] 库存明细表格组件
- [x] 主页面集成
- [x] 样式和布局优化
- [ ] API接口对接（需要后端配合）
- [ ] 单元测试编写

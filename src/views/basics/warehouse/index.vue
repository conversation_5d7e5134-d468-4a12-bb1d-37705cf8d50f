<template>
    <div class="app-container">
        <el-card shadow="never" class="box-card form-card mb10">
            <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                class="form_box"
                >
                <el-row class=" form_row">
                    <el-row class="form_col">
                        <el-form-item prop="warehouseName">
                            <el-input
                            v-model="queryParams.warehouseName"
                            placeholder="仓库名称"
                            clearable
                            />
                        </el-form-item>
                        <el-form-item prop="warehouseType">
                            <el-select v-model="queryParams.warehouseType" placeholder="仓库类型">
                                <el-option v-for="(item, index) in warehouseTypeList" :key="index" :label="item.text" :value="item.value" />
                            </el-select>
                        </el-form-item> 
                    </el-row>
                </el-row>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        <template v-if="toggleSearchDom">
                            <el-button type="text" @click="packUp">
                                {{ toggleSearchStatus ? '收起' : '展开' }}
                                <i
                                :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                                ></i>
                            </el-button>
                        </template>
                    </el-form-item>
                </el-row>
            </el-form>
        </el-card>
        <el-card shadow="never" class="table_box">
            <el-row :gutter="10" class="mb8 form_btn">
                <el-col :span="24" class="fend">
                    <el-button class="default_btn" size="mini" icon="el-icon-plus" @click="addEnclosure">新建</el-button>
                    <el-button  class="default_device_btn" circle icon="el-icon-setting" size="mini" @click="setUp"></el-button>
                </el-col>
            </el-row>
            <!-- 表格数据 -->
            <div :style="{height: tableHeight + 'px'}">
                <el-table
                    :data="tableData"
                    stripe
                    style="width: 100%"
                    v-loading="loading"
                    :max-height="tableHeight"
                    border
                >
                    <el-table-column type="index" width="55" align="center" label="序号"></el-table-column>
                    <!-- <el-table-column prop="warehouseName" label="仓库名称" />
                    <el-table-column prop="warehouseType" label="仓库类型" :formatter="formatType"></el-table-column>
                    <el-table-column prop="remark" label="仓库描述" />
                    <el-table-column prop="updateUserName" label="创建人"></el-table-column>
                    <el-table-column prop="updateTime" label="更新时间"></el-table-column> -->
                    <el-table-column show-overflow-tooltip v-for="(item, index) in tableColumn" :key="index" :align="item.align" :sortable="item.sortable" :prop="item.prop" :label="item.label" />
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button class="edit_text_btn" size="mini" icon="el-icon-edit" @click="editEnclosure(scope.row)" type="text" >编辑</el-button>
                            <el-button class="delete_text_btn" size="mini" icon="el-icon-delete" @click="deleteEnclosure(scope.row)" type="text" >删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
            />
        </el-card>
        <Model ref="modelRef" :data="currentData" @getList='getList'></Model>
        <draggableModel ref="draggableModel" @changeTable='changeTable' :list='canChangeList' :defaultList='defaultList' id='10007'></draggableModel>
    </div>
</template>
  
<script>
import Model from "./model.vue";
import { warehouseList, warehouseDelete } from "@/api/basics/index.js";
import draggableModel from '../../../components/draggableTable.vue'
import { getMenuList } from "@/api/system/customMenu/data.js"
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
    mixins: [tableUi],
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                // requestRole: 2,
                warehouseName: '',
                warehouseType: '',
            },
            defaultList: [],
            canChangeList: [],
            tableColumn: [
                { check: true, prop: "warehouseName", label: "仓库名称", align: 'center'  },
                { check: true, prop: "warehouseTypeName", label: "仓库类型", align: 'center' },
                { check: true, prop: "managerUserName", label: "库管员" },
                { check: true, prop: "remark", label: "仓库描述" },
                { check: true, prop: "safetyStock", label: "安全库存" },
                { check: true, prop: "updateUserName", label: "创建人", align: 'center' },
                { check: true, prop: "updateTime", label: "更新时间", align: 'center', sortable: true},
            ],
            tableData: [],
            loading: true,
            total: 0,
            currentData:{},
            warehouseTypeList: [
                { text: "排酸库", value: 1 },
                { text: "急冻库", value: 2 },
                { text: "成品库", value: 3 },
                { text: "其他", value: 4 },
            ],
        };
    },
    components: {
        Model,
        draggableModel
    },
    created() {
		this.queryParams.requestRole = this.$route.query.requestRole
        this.defaultList = JSON.parse(JSON.stringify(this.tableColumn));
        this.canChangeList = JSON.parse(JSON.stringify(this.tableColumn));
        this.getMenu()
        this.getList();
    },
    methods: {
        //列表查询
        getList() {
            warehouseList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list.map((i) => {
                        this.warehouseTypeList.forEach((item) => {
                            if (item.value == i.warehouseType) {
                                i.warehouseTypeName = item.text;
                            }
                        });
                        return i
                    });
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        reset() {
            this.$refs.queryForm.resetFields();
        },
        //新增
        addEnclosure() {
            this.currentData = {}
            this.$refs.modelRef.showModel()
        },
        //编辑
        editEnclosure(row) {
            this.currentData = row
            this.$refs.modelRef.showModel()
        },
        //重置
        resetQuery() {
            this.reset();
            this.handleQuery();
        },
        //刷新页面
        refreshList() {
            this.getList();
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        deleteEnclosure(row) {
            this.$confirm('是否删除该仓库？ 删除后不可恢复！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                warehouseDelete({
                    warehouseId: row.warehouseId
                }).then(() => {
                    this.$message.success('已删除')
                    this.getList()
                })
            })
        },
        getMenu() {
            getMenuList({
                businessType: 10007
            }).then((res) => {
                this.changeTable(JSON.parse(res.result))
            })
        },
        setUp() {
            this.$refs.draggableModel.showModel()
        },
        changeTable(dataList) {
            if (!dataList || dataList.length <= 0) { return }
            this.canChangeList = dataList
            this.tableColumn = []
            dataList.forEach(i => {
                if (i.check) {
                    this.tableColumn.push(i)
                }
            })
        },
    },
};
</script>
  
<style lang="scss" scoped>
</style>
  